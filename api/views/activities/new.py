from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.contenttypes.models import ContentType

from activities.serializers import ActivityLogSerializer, ActivityLogCreateSerializer
from activities.services import ActivityService
from activities.models import ActivityType
from base.services.auth import verify_user
from base.services.forms import check_missing_fields
from base.services.logging.logger import LoggingService
from base.utils.model_mapping import MODEL_MAPPING

logging_service = LoggingService()


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def new_activity(request):
    """
    Enhanced activity creation endpoint that supports both legacy and new formats.
    """
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    try:
        data = request.data.copy()

        if 'content_type_id' in data and 'object_id' in data:
            content_type = ContentType.objects.get(id=data['content_type_id'])
            model_class = content_type.model_class()
            content_object = model_class.objects.get(id=data['object_id'])

            activity_type = data.get('activity_type', ActivityType.UPDATED)
            description = data.get('description')
            details = data.get('details', {})

            activity = ActivityService.log_activity(
                user=user,
                content_object=content_object,
                activity_type=activity_type,
                description=description,
                details=details,
                ip_address=data.get('ip_address'),
                user_agent=data.get('user_agent'),
            )

            serializer = ActivityLogSerializer(activity)

            return Response(
                {
                    "status": "success",
                    "message": "Activity logged successfully",
                    "data": serializer.data,
                },
                status=status.HTTP_201_CREATED,
            )

        else:
            required_fields = [
                "action",
                "ip_address",
                "user_agent",
                "description",
                "incident_id",
            ]

            missing_fields_response = check_missing_fields(
                data=request.data, required_fields=required_fields
            )

            if check_missing_fields(request.data, required_fields):
                return missing_fields_response

            data["user"] = user.id
            new_activity = ActivityLogCreateSerializer(data=data)

            if new_activity.is_valid():
                activity = new_activity.save(user=user)
                serializer = ActivityLogSerializer(activity)

                return Response(
                    {
                        "status": "success",
                        "message": "Activity logged successfully",
                        "data": serializer.data,
                    },
                    status=status.HTTP_201_CREATED,
                )
            else:
                return Response(
                    {
                        "message": "Failed to create activity",
                        "errors": new_activity.errors,
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

    except ContentType.DoesNotExist:
        return Response(
            {"error": "Invalid content type"},
            status=status.HTTP_400_BAD_REQUEST,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def log_activity_for_incident(request, incident_type, incident_id):
    """
    Convenient endpoint to log activities for specific incident types.
    """
    access_token = request.headers.get("Authorization")
    user, message = verify_user(access_token)

    if user is None:
        return Response(
            {"status": "failed", "message": message},
            status=status.HTTP_401_UNAUTHORIZED,
        )

    if incident_type not in MODEL_MAPPING:
        return Response(
            {"error": "Invalid incident type"},
            status=status.HTTP_400_BAD_REQUEST,
        )

    try:
        model_class = MODEL_MAPPING[incident_type]
        content_object = model_class.objects.get(id=incident_id)

        data = request.data
        activity_type = data.get('activity_type', ActivityType.UPDATED)
        description = data.get('description')
        details = data.get('details', {})

        activity = ActivityService.log_activity(
            user=user,
            content_object=content_object,
            activity_type=activity_type,
            description=description,
            details=details,
            ip_address=data.get('ip_address'),
            user_agent=data.get('user_agent'),
        )

        serializer = ActivityLogSerializer(activity)

        return Response(
            {
                "status": "success",
                "message": "Activity logged successfully",
                "data": serializer.data,
            },
            status=status.HTTP_201_CREATED,
        )

    except model_class.DoesNotExist:
        return Response(
            {"error": f"{incident_type} with id {incident_id} not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Internal server error"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
