from django.test import TestCase
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from activities.models import ActivityLog, ActivityType
from general_patient_visitor.models import GeneralPatientVisitor
from base.models import Facility, Department


class ActivityLogModelTest(TestCase):
    """Test cases for the ActivityLog model"""
    
    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.facility = Facility.objects.create(
            name='Test Facility',
            address='123 Test St'
        )
        
        self.department = Department.objects.create(
            name='Test Department',
            facility=self.facility
        )
        
        self.incident = GeneralPatientVisitor.objects.create(
            incident_type='General Patient/Visitor',
            status='Draft',
            report_facility=self.facility,
            department=self.department,
            created_by=self.user
        )
    
    def test_activity_log_creation(self):
        """Test creating an activity log entry"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            description='Test incident created',
            content_object=self.incident,
            facility=self.facility,
            department=self.department
        )
        
        self.assertEqual(activity.user, self.user)
        self.assertEqual(activity.activity_type, ActivityType.CREATED)
        self.assertEqual(activity.content_object, self.incident)
        self.assertEqual(activity.facility, self.facility)
        self.assertEqual(activity.department, self.department)
        self.assertIsNotNone(activity.timestamp)
    
    def test_activity_log_str_representation(self):
        """Test the string representation of ActivityLog"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            description='Test incident created',
            content_object=self.incident
        )
        
        expected_str = f"{self.user.email} - created - {activity.timestamp}"
        self.assertEqual(str(activity), expected_str)
    
    def test_get_formatted_description(self):
        """Test the formatted description method"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.STATUS_CHANGED,
            content_object=self.incident,
            details={
                'old_status': 'Draft',
                'new_status': 'Open'
            }
        )
        
        description = activity.get_formatted_description()
        self.assertIn('status changed', description.lower())
        self.assertIn('Draft', description)
        self.assertIn('Open', description)
    
    def test_get_activity_icon(self):
        """Test the activity icon method"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            content_object=self.incident
        )
        
        icon = activity.get_activity_icon()
        self.assertIsInstance(icon, str)
        self.assertTrue(len(icon) > 0)
    
    def test_activity_with_details(self):
        """Test activity log with JSON details"""
        details = {
            'incident_type': 'General Patient/Visitor',
            'changed_fields': ['status', 'description'],
            'metadata': {'source': 'web_interface'}
        }
        
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.UPDATED,
            content_object=self.incident,
            details=details
        )
        
        self.assertEqual(activity.details, details)
        self.assertEqual(activity.details['incident_type'], 'General Patient/Visitor')
        self.assertEqual(len(activity.details['changed_fields']), 2)
    
    def test_activity_without_user(self):
        """Test activity log creation without a user (system activity)"""
        activity = ActivityLog.objects.create(
            activity_type=ActivityType.NOTIFICATION_SENT,
            description='System notification sent',
            content_object=self.incident
        )
        
        self.assertIsNone(activity.user)
        self.assertEqual(activity.activity_type, ActivityType.NOTIFICATION_SENT)
    
    def test_activity_with_target_user(self):
        """Test activity log with target user"""
        target_user = User.objects.create_user(
            username='targetuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.ASSIGNED,
            content_object=self.incident,
            target_user=target_user,
            details={'assignee_name': 'Target User'}
        )
        
        self.assertEqual(activity.target_user, target_user)
        self.assertEqual(activity.details['assignee_name'], 'Target User')
    
    def test_activity_ordering(self):
        """Test that activities are ordered by timestamp descending"""
        activity1 = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            content_object=self.incident
        )
        
        activity2 = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.UPDATED,
            content_object=self.incident
        )
        
        activity3 = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.STATUS_CHANGED,
            content_object=self.incident
        )
        
        activities = ActivityLog.objects.filter(content_object=self.incident)
        
        self.assertEqual(activities[0], activity3)
        self.assertEqual(activities[1], activity2)
        self.assertEqual(activities[2], activity1)
    
    def test_content_type_relationship(self):
        """Test the generic foreign key relationship"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            content_object=self.incident
        )
        
        expected_content_type = ContentType.objects.get_for_model(GeneralPatientVisitor)
        self.assertEqual(activity.content_type, expected_content_type)
        self.assertEqual(activity.object_id, self.incident.id)
        self.assertEqual(activity.content_object, self.incident)
    
    def test_activity_type_choices(self):
        """Test that all activity type choices are valid"""
        for choice_value, choice_label in ActivityType.choices:
            activity = ActivityLog.objects.create(
                user=self.user,
                activity_type=choice_value,
                content_object=self.incident,
                description=f'Test {choice_label}'
            )
            self.assertEqual(activity.activity_type, choice_value)
    
    def test_activity_meta_options(self):
        """Test model meta options"""
        activity = ActivityLog.objects.create(
            user=self.user,
            activity_type=ActivityType.CREATED,
            content_object=self.incident
        )
        
        self.assertEqual(ActivityLog._meta.verbose_name, 'Activity Log')
        self.assertEqual(ActivityLog._meta.verbose_name_plural, 'Activity Logs')
        
        index_names = [index.name for index in ActivityLog._meta.indexes]
        self.assertTrue(any('content' in name for name in index_names))
        self.assertTrue(any('activity' in name for name in index_names))
        self.assertTrue(any('timestamp' in name for name in index_names))
