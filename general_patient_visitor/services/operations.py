from accounts.models import Profile, UserProfile
from accounts.repositories.familty_member import PatientFamilyRepository
from accounts.repositories.patient_profile import PatientProfileRepository
from accounts.repositories.profile import ProfileRepository
from accounts.services.user_profile.service import UserProfileService
from api.views.auth.permissions_list import (
    is_admin_user,
    is_manager_user,
    is_super_user,
)
from api.views.incidents.general_incident.new_incident import (
    check_anonymous,
    get_patient_profile,
)
from base.models import Department, Facility
from base.services.forms import check_user_facility
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.notifications import save_notification
from documents.models import Document
from general_patient_visitor.repositories.repository import IncidentRepository
from general_patient_visitor.serializers import (
    GeneralPatientVisitorVersionSerializer,
    IncidentSerializer,
    IncidentListSerializer,
    IncidentUpdateSerializer,
    NewGeneralPatientVisitorSerializer,
)
from base.services.logging.logger import LoggingService
from base.services.responses import APIResponse, RepositoryResponse
from general_patient_visitor.models import GeneralPatientVisitor
from incidents.services.query import IncidentQueryService
from incidents.views.send_to_department import send_incident_submission_email
from django.contrib.auth.models import User
from activities.services import ActivityService
from activities.models import ActivityType

from reviews.models import Review
from tasks.models import ReviewProcessTasks

incidents_service = GetIncidentsService()
logging_service = LoggingService()
patient_repository = PatientProfileRepository()
user_profile_service = UserProfileService()


class GPVService:
    def __init__(self, user):
        self.logging_service = LoggingService()
        self.user = user
        self.query_service = IncidentQueryService(
            user=user,
            model=GeneralPatientVisitor,
            serializer=IncidentListSerializer,
        )
        self.incident_repository = IncidentRepository()
        self.profile_repository = ProfileRepository()
        self.patient_repository = PatientProfileRepository()
        self.family_member_repository = PatientFamilyRepository()

    """A service class for General Patient Visitor model"""

    def get_incident_by_id(self, incident_id) -> APIResponse:
        try:
            incident = self.query_service.get_incident_by_id(
                incident_id=incident_id,
            )
            if not incident.success:
                return APIResponse(
                    success=False,
                    message=incident.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incident retrieved successfully",
                data=incident.data,
                code=200,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def get_incidents_list(
        self,
        filters=None,
    ):
        try:
            incidents = self.query_service.get_incidents(
                user=self.user,
                model=GeneralPatientVisitor,
                serializer=IncidentListSerializer,
                prefetch_fields=[
                    "documents",
                    "reviews",
                ],
                related_fields=[
                    "report_facility",
                    "department",
                    "patient_visitor",
                    "created_by",
                    "physician_notified",
                    "family_notified",
                    "notified_by",
                ],
                filters=filters,
            )
            if not incidents.success:
                return APIResponse(
                    success=False,
                    message=incidents.message,
                    data=None,
                    code=400,
                )

            return APIResponse(
                success=True,
                message="Incidents retrieved successfully",
                data=incidents.data,
                code=200,
            )
        except Exception as e:
            LoggingService().log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )

    def create_incident(self, data: dict, user: User) -> APIResponse:
        request_metadata = data.pop("meta", None)
        try:
            # Process the data before sending to repository
            processed_data = data.copy()
            document_ids = processed_data.pop("documents", [])
            review_ids = processed_data.pop("reviews", [])
            review_task_ids = processed_data.pop("review_tasks", [])
            patient_visitor = None
            physician_notified = None
            family_notified = None
            notified_by = None
            facility = None

            is_facility_or_report_facility_id = any(
                key in ["facility_id", "report_facility_id"] for key in data.keys()
            )
            if not is_facility_or_report_facility_id:
                return APIResponse(
                    success=False,
                    message="Facility id is required",
                    code=400,
                )
            if "facility_id" in data:
                processed_data.pop("facility_id", None)
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                processed_data["report_facility"] = facility

            if "department" in data:
                department_response = Department.objects.get(id=data["department"])
                if not department_response:
                    return APIResponse(
                        success=False,
                        message=department_response,
                        code=400,
                    )
                processed_data["department"] = department_response

            if "patient_visitor" in processed_data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient_visitor"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=400,
                    )
                processed_data["patient_visitor"] = patient_profile.data
            if "family_notified" in processed_data:
                if not patient_profile:
                    return APIResponse(
                        success=False,
                        message="Patient or visitor is required in for family member to be recorded",
                        code=400,
                    )
                family_profile = user_profile_service.get_or_create_profile(
                    data["family_notified"]
                )
                if not family_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_profile.message,
                        code=400,
                    )
                processed_data["family_notified"] = family_profile.data

            if "physician_notified" in processed_data:
                physician_profile = user_profile_service.get_or_create_profile(
                    data["physician_notified"]
                )
                if not physician_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_profile.message,
                        code=400,
                    )
                processed_data["physician_notified"] = physician_profile.data

            if "notified_by" in processed_data:
                notified_by_response = user_profile_service.get_or_create_profile(
                    data["notified_by"]
                )
                if not notified_by_response.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_response.message,
                        code=400,
                    )
                notified_by = notified_by_response.data
            # add related data to the incident data

            if notified_by:
                processed_data["notified_by"] = notified_by

            # add metadata to the incident data
            processed_data["created_by"] = user

            """
            1. Check if incident data has incident_id
            2. if yes, if yes, create a version
            3. if no, if yes, create a new incident
            """

            if "incident_id" in processed_data:

                processed_data["original_report_id"] = processed_data.pop(
                    "incident_id", None
                )
                version = self.incident_repository.create_incident_version(
                    processed_data
                )
                if not version.success:
                    return RepositoryResponse(
                        success=False, message=version.message, data=None
                    )
                serializer = GeneralPatientVisitorVersionSerializer(version.data)
                return RepositoryResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serializer.data,
                )
            else:
                incident = self.incident_repository.create_incident(processed_data)
                if not incident.success:
                    return APIResponse(
                        success=False,
                        message=incident.message,
                        code=400,
                    )
                incident_instance = incident.data
                if document_ids:
                    incident_instance.documents.set(
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids:
                    incident_instance.reviews.set(
                        Review.objects.filter(id__in=review_ids)
                    )
                if review_task_ids:
                    incident_instance.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )

                # Create notification for new incident
                save_notification(
                    facility=facility,
                    group_name="Quality/Risk Management",
                    notification_type="info",
                    notification_category="incident",
                    message="A new incident is submitted",
                    item_id=incident.data.id,
                )

                ActivityService.log_creation(
                    user=self.user,
                    content_object=incident.data,
                    facility=facility,
                    details={
                        'incident_type': 'General Patient/Visitor',
                        'status': incident.data.status,
                    }
                )

                if document_ids:
                    ActivityService.log_document_activity(
                        user=self.user,
                        content_object=incident.data,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'General Patient/Visitor'}
                    )

                serializer = IncidentListSerializer(incident.data)
                return APIResponse(
                    success=True,
                    message="Incident created successfully",
                    data=serializer.data,
                    code=201,
                )

        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the incident",
                errors={"detail": "Internal server error"},
            )

    def update_incident(self, incident_id: int, data: dict, user: User) -> APIResponse:
        try:
            request_data = data.copy()
            general_patient_visitor = GeneralPatientVisitor.objects.get(id=incident_id)
            profile = Profile.objects.get(user=user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, general_patient_visitor.department)
                and not self.user == general_patient_visitor.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )
            document_ids = request_data.pop("documents", None)
            review_ids = request_data.pop("reviews", None)
            review_task_ids = request_data.pop("review_tasks", None)
            if "patient_visitor" in data:
                patient_profile = user_profile_service.get_or_create_profile(
                    data["patient_visitor"]
                )
                if not patient_profile.success:
                    return APIResponse(
                        success=False,
                        message=patient_profile.message,
                        code=400,
                    )
                request_data["patient_visitor"] = patient_profile.data.id
            if "family_notified" in data:
                family_profile = user_profile_service.get_or_create_profile(
                    data["family_notified"]
                )
                if not family_profile.success:
                    return APIResponse(
                        success=False,
                        message=family_profile.message,
                        code=400,
                    )
                request_data["family_notified"] = family_profile.data.id
            if "physician_notified" in data:
                physician_profile = user_profile_service.get_or_create_profile(
                    data["physician_notified"]
                )
                if not physician_profile.success:
                    return APIResponse(
                        success=False,
                        message=physician_profile.message,
                        code=400,
                    )
                request_data["physician_notified"] = physician_profile.data.id

            if "notified_by" in data:
                notified_by_response = user_profile_service.get_or_create_profile(
                    data["notified_by"]
                )
                if not notified_by_response.success:
                    return APIResponse(
                        success=False,
                        message=notified_by_response.message,
                        code=400,
                    )
                request_data["notified_by"] = notified_by_response.data.id
            if "facility_id" in data:
                facility_response = self._process_facility(data["facility_id"])
                if not facility_response.success:
                    return APIResponse(
                        success=False,
                        message=facility_response.message,
                        code=400,
                    )
                facility = facility_response.data
                request_data["report_facility"] = facility
            if "department" in data:
                department_response = Department.objects.get(id=data["department"])
                if not department_response:
                    return APIResponse(
                        success=False,
                        message=department_response,
                        code=400,
                    )
                request_data["department"] = department_response.id

            request_data["updated_by"] = user
            data = check_anonymous(request_data, self.user)
            serializer = GeneralPatientVisitorVersionSerializer(
                general_patient_visitor, data=data, partial=True
            )
            if serializer.is_valid():
                serializer.save()
                if document_ids is not None:
                    general_patient_visitor.documents.set(
                        Document.objects.filter(id__in=document_ids)
                    )
                if review_ids is not None:
                    general_patient_visitor.reviews.set(
                        Review.objects.filter(id__in=review_ids)
                    )

                if review_task_ids is not None:
                    general_patient_visitor.review_tasks.set(
                        ReviewProcessTasks.objects.filter(id__in=review_task_ids)
                    )
                if "status" in data:
                    old_status = general_patient_visitor.status
                    new_status = data.get("status")

                    if old_status != new_status:
                        ActivityService.log_status_change(
                            user=user,
                            content_object=general_patient_visitor,
                            old_status=old_status,
                            new_status=new_status,
                            details={'incident_type': 'General Patient/Visitor'}
                        )

                    if new_status == "Open":
                        send_incident_submission_email(
                            incident=general_patient_visitor,
                            incident_type=data.get("incident_type"),
                        )

                        # Log submission activity
                        ActivityService.log_activity(
                            user=user,
                            content_object=general_patient_visitor,
                            activity_type=ActivityType.NOTIFICATION_SENT,
                            description="Incident submission notification sent",
                            details={
                                'incident_type': 'General Patient/Visitor',
                                'notification_type': 'submission_email'
                            }
                        )

                # Log general update activity
                changed_fields = list(data.keys())
                ActivityService.log_update(
                    user=user,
                    content_object=general_patient_visitor,
                    changed_fields=changed_fields,
                    details={
                        'incident_type': 'General Patient/Visitor',
                        'update_type': 'direct_update'
                    }
                )

                # Log document changes if any
                if document_ids is not None:
                    ActivityService.log_document_activity(
                        user=user,
                        content_object=general_patient_visitor,
                        document_count=len(document_ids),
                        is_addition=True,
                        details={'incident_type': 'General Patient/Visitor'}
                    )

                serialized_data = IncidentListSerializer(general_patient_visitor)
                return APIResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=None,
                    code=400,
                )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Profile not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while updating the incident",
                data=None,
                code=500,
            )

    def _process_patient_visitor(self, data):
        patient_data = None

        try:
            """This code was being used to restrict users to create invalid MRN"""
            # if "medical_record_number" in data and data.get(
            #     "medical_record_number", None
            # ):
            #     existing_patient_visitor = self.patient_repository.get_patient_by_mrn(
            #         data["medical_record_number"]
            #     )
            #     if not existing_patient_visitor.success:
            #         return APIResponse(
            #             success=False,
            #             message="Patient not found. Leave MRN field empty to create a new one",
            #             code=404,
            #             data=None,
            #         )
            # patient_update_response = patient_service.update_patient(
            #     existing_patient_visitor.data.id, data
            # )
            # if not patient_update_response.success:
            #     return APIResponse(
            #         success=False,
            #         message=patient_update_response.message,
            #         code=400,
            #         data=None,
            #     )
            # patient_data = existing_patient_visitor.data
            # else:
            mew_patient_visitor = self.patient_repository.create_patient(data)
            if not mew_patient_visitor.success:
                return RepositoryResponse(
                    success=False,
                    message="Failed to create patient",
                    data=None,
                )
            patient_data = mew_patient_visitor.data
            return RepositoryResponse(
                success=True,
                message="Patient created successfully",
                data=patient_data,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the patient_visitor",
                errors={"detail": "Internal server error"},
            )

    def _process_facility(self, facility_id):
        try:
            facility = Facility.objects.get(id=facility_id)

            return RepositoryResponse(
                success=True, message="Facility retrieved", data=facility
            )
        except Facility.DoesNotExist:
            return APIResponse(
                success=False,
                message="Facility not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while retrieving the facility",
                errors={"detail": "Internal server error"},
            )

    def _process_family_member(self, patient, data) -> RepositoryResponse:
        member = None
        try:
            member = UserProfile.objects.filter(
                patient=patient,
                first_name=data.get("first_name"),
                last_name=data.get("last_name"),
            ).first()
            if not member:
                family_data = data.copy()
                family_data["patient"] = patient
                family_member = self.family_member_repository.create_family_member(
                    family_data
                )
                if not family_member.success:
                    return RepositoryResponse(
                        success=False,
                        message="Failed to create family member",
                        data=None,
                    )
                member = family_member.data
            return RepositoryResponse(
                success=True, message="Family member created successfully", data=member
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message="An error occurred while processing family member",
                data=None,
            )

    def create_version(self, incident_id, incident_data, user):
        """
        1. Check if incident exists and get it's id, facility id
        2. Then create a new version of the incident using create method
        3. Modify the incident data to include "original_report", "report_facility", "created_by"
        4. Update the incident with updated_by at and updated_by
        5. Return the new version data
        """

        try:
            incident = GeneralPatientVisitor.objects.get(id=incident_id)

            if (
                not is_super_user(user)
                and not is_admin_user(user, incident.report_facility)
                and not is_manager_user(user, incident.department)
            ) and not incident.created_by == user:
                return APIResponse(
                    success=False,
                    message="You are not authorized to create a version of this incident",
                    code=403,
                )

            incident_data["incident_id"] = incident.id
            incident_data["report_facility_id"] = incident.report_facility.id
            incident_data["created_by_id"] = user.id
            incident_data["status"] = incident_data.get("status", "Draft")

            response = self.create_incident(incident_data, user)
            if not response.success:
                return APIResponse(
                    success=False,
                    message=response.message,
                    errors=response.data,
                )

            #  check status
            if "status" in incident_data:

                incident.status = incident_data["status"]
                incident.save()
                if incident_data["status"] == "Open":
                    send_incident_submission_email(
                        incident=incident, incident_type="General Patient/Visitor"
                    )

            return APIResponse(
                success=True,
                message="Incident version created successfully",
                data=response.data,
            )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                code=404,
            )
        except Exception as e:
            logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="An error occurred while creating the incident version",
                errors={"detail": "Internal server error"},
            )

        try:
            incident = GeneralPatientVisitor.objects.get(id=id)
            profile = Profile.objects.get(user=self.user)
            if (
                not is_super_user(self.user)
                and not is_admin_user(self.user, profile.facility)
                and not is_manager_user(self.user, incident.department)
                and not self.user == incident.created_by
            ):
                return APIResponse(
                    success=False,
                    message="You do not have enough rights to update this incident",
                    data=None,
                    code=403,
                )

            facility_response = check_user_facility(data, self.user)
            if not facility_response.success:
                return APIResponse(
                    success=False,
                    message=facility_response.message,
                    data=None,
                    code=400,
                )
            facility = facility_response.data

            request_data = data.copy()

            if "patient_visitor" in data:
                patient_profile, message = get_patient_profile(
                    data["patient_visitor"], facility
                )
                if patient_profile:
                    request_data["patient_visitor"] = patient_profile.id

            if "physician_notified" in data:
                physician_notified_profile, message = get_patient_profile(
                    data["physician_notified"], facility
                )
                if physician_notified_profile:
                    request_data["physician_notified"] = physician_notified_profile.id

            if "family_notified" in data:
                family_notified_profile, message = get_patient_profile(
                    data["family_notified"], facility
                )
                if family_notified_profile:
                    request_data["family_notified"] = family_notified_profile.id
            data = check_anonymous(request_data, self.user)
            serializer = IncidentUpdateSerializer(incident, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()
                if "status" in data and data.get("status") == "Open":
                    send_incident_submission_email(
                        incident=incident,
                        incident_type=data.get("incident_type"),
                    )
                serialized_data = IncidentListSerializer(incident)
                return APIResponse(
                    success=True,
                    message="Incident updated successfully",
                    data=serialized_data.data,
                    code=200,
                )
            else:
                self.logging_service.log_error(serializer.errors)
                return APIResponse(
                    success=False,
                    message="Invalid data",
                    data=None,
                    code=400,
                )
        except GeneralPatientVisitor.DoesNotExist:
            return APIResponse(
                success=False,
                message="Incident not found",
                data=None,
                code=404,
            )
        except Profile.DoesNotExist:
            return APIResponse(
                success=False,
                message="Profile not found",
                data=None,
                code=404,
            )
        except Exception as e:
            self.logging_service.log_error(e)
            return APIResponse(
                success=False,
                message="Internal server error",
                data=None,
                code=500,
            )
